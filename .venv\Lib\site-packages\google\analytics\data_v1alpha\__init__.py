# -*- coding: utf-8 -*-
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
from google.analytics.data_v1alpha import gapic_version as package_version

__version__ = package_version.__version__


from .services.alpha_analytics_data import (
    AlphaAnalyticsDataAsyncClient,
    AlphaAnalyticsDataClient,
)
from .types.analytics_data_api import (
    AudienceDimension,
    AudienceDimensionValue,
    AudienceList,
    AudienceListMetadata,
    AudienceRow,
    CreateAudienceListRequest,
    CreateRecurringAudienceListRequest,
    CreateReportTaskRequest,
    GetAudienceListRequest,
    GetPropertyQuotasSnapshotRequest,
    GetRecurringAudienceListRequest,
    GetReportTaskRequest,
    ListAudienceListsRequest,
    ListAudienceListsResponse,
    ListRecurringAudienceListsRequest,
    ListRecurringAudienceListsResponse,
    ListReportTasksRequest,
    ListReportTasksResponse,
    PropertyQuotasSnapshot,
    QueryAudienceListRequest,
    QueryAudienceListResponse,
    QueryReportTaskRequest,
    QueryReportTaskResponse,
    RecurringAudienceList,
    ReportTask,
    ReportTaskMetadata,
    RunFunnelReportRequest,
    RunFunnelReportResponse,
    SheetExportAudienceListRequest,
    SheetExportAudienceListResponse,
    WebhookNotification,
)
from .types.data import (
    BetweenFilter,
    Cohort,
    CohortReportSettings,
    CohortSpec,
    CohortsRange,
    DateRange,
    Dimension,
    DimensionExpression,
    DimensionHeader,
    DimensionValue,
    EmptyFilter,
    EventCriteriaScoping,
    EventExclusionDuration,
    EventSegment,
    EventSegmentConditionGroup,
    EventSegmentCriteria,
    EventSegmentExclusion,
    Filter,
    FilterExpression,
    FilterExpressionList,
    Funnel,
    FunnelBreakdown,
    FunnelEventFilter,
    FunnelFieldFilter,
    FunnelFilterExpression,
    FunnelFilterExpressionList,
    FunnelNextAction,
    FunnelParameterFilter,
    FunnelParameterFilterExpression,
    FunnelParameterFilterExpressionList,
    FunnelResponseMetadata,
    FunnelStep,
    FunnelSubReport,
    InListFilter,
    Metric,
    MetricAggregation,
    MetricHeader,
    MetricType,
    MetricValue,
    NumericFilter,
    NumericValue,
    OrderBy,
    PropertyQuota,
    QuotaStatus,
    ResponseMetaData,
    RestrictedMetricType,
    Row,
    SamplingLevel,
    SamplingMetadata,
    Segment,
    SegmentEventFilter,
    SegmentFilter,
    SegmentFilterExpression,
    SegmentFilterExpressionList,
    SegmentFilterScoping,
    SegmentParameterFilter,
    SegmentParameterFilterExpression,
    SegmentParameterFilterExpressionList,
    SegmentParameterFilterScoping,
    SessionCriteriaScoping,
    SessionExclusionDuration,
    SessionSegment,
    SessionSegmentConditionGroup,
    SessionSegmentCriteria,
    SessionSegmentExclusion,
    StringFilter,
    UserCriteriaScoping,
    UserExclusionDuration,
    UserSegment,
    UserSegmentConditionGroup,
    UserSegmentCriteria,
    UserSegmentExclusion,
    UserSegmentSequenceGroup,
    UserSequenceStep,
)

__all__ = (
    "AlphaAnalyticsDataAsyncClient",
    "AlphaAnalyticsDataClient",
    "AudienceDimension",
    "AudienceDimensionValue",
    "AudienceList",
    "AudienceListMetadata",
    "AudienceRow",
    "BetweenFilter",
    "Cohort",
    "CohortReportSettings",
    "CohortSpec",
    "CohortsRange",
    "CreateAudienceListRequest",
    "CreateRecurringAudienceListRequest",
    "CreateReportTaskRequest",
    "DateRange",
    "Dimension",
    "DimensionExpression",
    "DimensionHeader",
    "DimensionValue",
    "EmptyFilter",
    "EventCriteriaScoping",
    "EventExclusionDuration",
    "EventSegment",
    "EventSegmentConditionGroup",
    "EventSegmentCriteria",
    "EventSegmentExclusion",
    "Filter",
    "FilterExpression",
    "FilterExpressionList",
    "Funnel",
    "FunnelBreakdown",
    "FunnelEventFilter",
    "FunnelFieldFilter",
    "FunnelFilterExpression",
    "FunnelFilterExpressionList",
    "FunnelNextAction",
    "FunnelParameterFilter",
    "FunnelParameterFilterExpression",
    "FunnelParameterFilterExpressionList",
    "FunnelResponseMetadata",
    "FunnelStep",
    "FunnelSubReport",
    "GetAudienceListRequest",
    "GetPropertyQuotasSnapshotRequest",
    "GetRecurringAudienceListRequest",
    "GetReportTaskRequest",
    "InListFilter",
    "ListAudienceListsRequest",
    "ListAudienceListsResponse",
    "ListRecurringAudienceListsRequest",
    "ListRecurringAudienceListsResponse",
    "ListReportTasksRequest",
    "ListReportTasksResponse",
    "Metric",
    "MetricAggregation",
    "MetricHeader",
    "MetricType",
    "MetricValue",
    "NumericFilter",
    "NumericValue",
    "OrderBy",
    "PropertyQuota",
    "PropertyQuotasSnapshot",
    "QueryAudienceListRequest",
    "QueryAudienceListResponse",
    "QueryReportTaskRequest",
    "QueryReportTaskResponse",
    "QuotaStatus",
    "RecurringAudienceList",
    "ReportTask",
    "ReportTaskMetadata",
    "ResponseMetaData",
    "RestrictedMetricType",
    "Row",
    "RunFunnelReportRequest",
    "RunFunnelReportResponse",
    "SamplingLevel",
    "SamplingMetadata",
    "Segment",
    "SegmentEventFilter",
    "SegmentFilter",
    "SegmentFilterExpression",
    "SegmentFilterExpressionList",
    "SegmentFilterScoping",
    "SegmentParameterFilter",
    "SegmentParameterFilterExpression",
    "SegmentParameterFilterExpressionList",
    "SegmentParameterFilterScoping",
    "SessionCriteriaScoping",
    "SessionExclusionDuration",
    "SessionSegment",
    "SessionSegmentConditionGroup",
    "SessionSegmentCriteria",
    "SessionSegmentExclusion",
    "SheetExportAudienceListRequest",
    "SheetExportAudienceListResponse",
    "StringFilter",
    "UserCriteriaScoping",
    "UserExclusionDuration",
    "UserSegment",
    "UserSegmentConditionGroup",
    "UserSegmentCriteria",
    "UserSegmentExclusion",
    "UserSegmentSequenceGroup",
    "UserSequenceStep",
    "WebhookNotification",
)
